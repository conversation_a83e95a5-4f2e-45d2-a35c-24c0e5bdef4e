<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Novassist - Transforming Healthcare Administration with AI</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 60px;
            background: white;
            position: absolute;
            top: 0;
            left: 0;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.5s ease-in-out;
        }

        .slide.active {
            display: flex;
            flex-direction: column;
            opacity: 1;
            transform: translateX(0);
        }

        .slide h1 {
            font-size: 3.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .slide h2 {
            font-size: 2.5em;
            color: #34495e;
            margin-bottom: 30px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 10px;
        }

        .slide h3 {
            font-size: 1.8em;
            color: #667eea;
            margin-bottom: 15px;
        }

        .slide p, .slide li {
            font-size: 1.3em;
            line-height: 1.6;
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .slide ul {
            margin-left: 30px;
            margin-bottom: 20px;
        }

        .slide li {
            margin-bottom: 10px;
        }

        /* Cover slide specific styles */
        .cover-slide {
            justify-content: center;
            align-items: center;
            text-align: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .cover-slide h1 {
            font-size: 5em;
            margin-bottom: 20px;
            -webkit-text-fill-color: white;
        }

        .cover-slide p {
            font-size: 2em;
            color: rgba(255, 255, 255, 0.9);
        }

        /* Table styles */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 1.2em;
        }

        th, td {
            border: 2px solid #667eea;
            padding: 15px;
            text-align: left;
        }

        th {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-weight: bold;
        }

        tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        /* Navigation */
        .nav-container {
            position: fixed;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 1000;
            display: flex;
            gap: 15px;
            align-items: center;
        }

        .nav-btn {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .nav-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            background: rgba(255, 255, 255, 0.9);
            padding: 8px 16px;
            border-radius: 20px;
            color: #2c3e50;
            font-weight: bold;
        }

        /* Special content styles */
        .highlight-box {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            border-left: 5px solid #667eea;
            padding: 20px;
            margin: 20px 0;
            border-radius: 5px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            margin: 20px 0;
        }

        .feature-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }

        .metric-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            margin: 20px 0;
        }

        .metric-box h3 {
            color: white;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .funding-breakdown {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            margin: 30px 0;
        }

        .funding-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border-top: 4px solid #667eea;
        }

        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.6s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(30px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .slide {
                padding: 30px;
            }
            
            .slide h1 {
                font-size: 2.5em;
            }
            
            .slide h2 {
                font-size: 2em;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
            
            .funding-breakdown {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- Slide 1: Cover -->
        <div class="slide active cover-slide">
            <h1>Novassist</h1>
            <p>Transforming Healthcare Administration with AI</p>
            <div style="margin-top: 40px; font-size: 1.2em;">
                <p>Intelligent Healthcare Administration Platform</p>
            </div>
        </div>

        <!-- Slide 2: The Problem -->
        <div class="slide">
            <h2>The Problem</h2>
            <div class="highlight-box">
                <ul>
                    <li><strong>30% of healthcare costs</strong> are tied to administrative tasks</li>
                    <li>Clinicians spend <strong>2-3 hours per day</strong> on clerical work</li>
                    <li>Patient data is <strong>fragmented</strong> and difficult to analyze holistically</li>
                    <li><strong>Documentation errors</strong> lead to compliance and care issues</li>
                </ul>
            </div>
            <div class="metric-box">
                <h3>$1.2 Trillion</h3>
                <p>Annual administrative waste in U.S. healthcare</p>
            </div>
        </div>

        <!-- Slide 3: Our Solution -->
        <div class="slide">
            <h2>Our Solution</h2>
            <div class="highlight-box">
                <h3>Novassist: AI Platform for Healthcare Administration</h3>
                <ul>
                    <li>Ingests and understands <strong>structured and unstructured</strong> patient data</li>
                    <li>Builds <strong>comprehensive, longitudinal</strong> patient profiles</li>
                    <li><strong>Reduces clinician burden</strong> and improves care accuracy</li>
                    <li>Automates complex <strong>administrative workflows</strong></li>
                </ul>
            </div>
        </div>

        <!-- Slide 4: Key Features -->
        <div class="slide">
            <h2>Key Features</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>Record Ingestion Engine</h3>
                    <p>OCR + NLP for PDFs, HL7, FHIR, clinical notes</p>
                </div>
                <div class="feature-item">
                    <h3>Patient Profile Builder</h3>
                    <p>Timeline creation, trend detection, gap analysis</p>
                </div>
                <div class="feature-item">
                    <h3>Automation Tools</h3>
                    <p>Intake forms, prior auths, auto documentation</p>
                </div>
                <div class="feature-item">
                    <h3>Interoperability Layer</h3>
                    <p>FHIR-compliant data exchange</p>
                </div>
            </div>
        </div>

        <!-- Slide 5: Market Opportunity -->
        <div class="slide">
            <h2>Market Opportunity</h2>
            <div class="feature-grid">
                <div class="metric-box">
                    <h3>$50B+</h3>
                    <p><strong>TAM:</strong> Global healthcare admin software</p>
                </div>
                <div class="metric-box">
                    <h3>$12B</h3>
                    <p><strong>SAM:</strong> US outpatient and mid-sized provider clinics</p>
                </div>
            </div>
            <div class="metric-box">
                <h3>$500M</h3>
                <p><strong>SOM:</strong> Target in 5 years (1,000 clinics)</p>
            </div>
        </div>

        <!-- Slide 6: Competitive Landscape -->
        <div class="slide">
            <h2>Competitive Landscape</h2>
            <table>
                <thead>
                    <tr>
                        <th>Company</th>
                        <th>Focus</th>
                        <th>Our Differentiator</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Nuance</strong></td>
                        <td>Voice documentation</td>
                        <td>End-to-end record understanding</td>
                    </tr>
                    <tr>
                        <td><strong>DeepScribe</strong></td>
                        <td>Note-taking</td>
                        <td>Multi-source integration</td>
                    </tr>
                    <tr>
                        <td><strong>Innovaccer</strong></td>
                        <td>Population health</td>
                        <td>Patient-level AI summaries</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Slide 7: Business Model -->
        <div class="slide">
            <h2>Business Model</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>SaaS Subscriptions</h3>
                    <p>Tiered pricing by patient volume</p>
                </div>
                <div class="feature-item">
                    <h3>Enterprise Licensing</h3>
                    <p>Custom deployments for large health systems</p>
                </div>
                <div class="feature-item">
                    <h3>AI API Usage</h3>
                    <p>Pay-per-use for third-party integrations</p>
                </div>
                <div class="feature-item">
                    <h3>Analytics Services</h3>
                    <p>De-identified data insights (opt-in only)</p>
                </div>
            </div>
        </div>

        <!-- Slide 8: Traction & Roadmap -->
        <div class="slide">
            <h2>Traction & Roadmap</h2>
            <div class="highlight-box">
                <h3>Current Status</h3>
                <ul>
                    <li>MVP launched with NLP ingestion and profile generation</li>
                    <li><strong>2 pilot programs</strong> with multi-specialty clinics</li>
                </ul>
            </div>
            <div class="highlight-box">
                <h3>Next 12 Months</h3>
                <ul>
                    <li>EHR integration development</li>
                    <li>SOC 2 compliance certification</li>
                    <li>Expand pilot base to 10+ clinics</li>
                </ul>
            </div>
        </div>

        <!-- Slide 9: Go-To-Market Strategy -->
        <div class="slide">
            <h2>Go-To-Market Strategy</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>Target Market</h3>
                    <p>Mid-size clinics and concierge practices</p>
                </div>
                <div class="feature-item">
                    <h3>Sales Approach</h3>
                    <p>Direct sales with clinical experts</p>
                </div>
            </div>
            <div class="highlight-box">
                <h3>Partnership Strategy</h3>
                <ul>
                    <li>Health IT partnerships</li>
                    <li>Conference presence (HIMSS, HLTH)</li>
                    <li>Clinical workflow demonstrations</li>
                </ul>
            </div>
        </div>

        <!-- Slide 10: Team -->
        <div class="slide">
            <h2>Team</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>CEO</h3>
                    <p>Health IT Expert - Digital health & hospital systems experience</p>
                </div>
                <div class="feature-item">
                    <h3>CTO</h3>
                    <p>AI/NLP Leader - Clinical NLP & healthcare AI expertise</p>
                </div>
                <div class="feature-item">
                    <h3>CMO</h3>
                    <p>Physician Advisor - Clinical ops & EHR workflow specialist</p>
                </div>
                <div class="feature-item">
                    <h3>Key Hires Planned</h3>
                    <p>Product Manager, Integration Engineer, Sales Lead</p>
                </div>
            </div>
        </div>

        <!-- Slide 11: Financials -->
        <div class="slide">
            <h2>Financials (3-Year Summary)</h2>
            <table>
                <thead>
                    <tr>
                        <th>Metric</th>
                        <th>Year 1</th>
                        <th>Year 2</th>
                        <th>Year 3</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>Revenue</strong></td>
                        <td>$500K</td>
                        <td>$3M</td>
                        <td>$12M</td>
                    </tr>
                    <tr>
                        <td><strong>Net Income</strong></td>
                        <td>-$1M</td>
                        <td>-$0.5M</td>
                        <td>$2M</td>
                    </tr>
                    <tr>
                        <td><strong>Clinics</strong></td>
                        <td>10</td>
                        <td>50</td>
                        <td>200</td>
                    </tr>
                    <tr>
                        <td><strong>Margin</strong></td>
                        <td>60%</td>
                        <td>70%</td>
                        <td>75%</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Slide 12: Funding Ask -->
        <div class="slide">
            <h2>Funding Ask</h2>
            <div class="metric-box">
                <h3>$3M Seed Round</h3>
            </div>
            <h3>Use of Funds</h3>
            <div class="funding-breakdown">
                <div class="funding-item">
                    <h3>40%</h3>
                    <p>Product Development</p>
                </div>
                <div class="funding-item">
                    <h3>25%</h3>
                    <p>GTM and Sales</p>
                </div>
                <div class="funding-item">
                    <h3>15%</h3>
                    <p>EHR Integrations</p>
                </div>
                <div class="funding-item">
                    <h3>10%</h3>
                    <p>Compliance & Legal</p>
                </div>
                <div class="funding-item">
                    <h3>10%</h3>
                    <p>Operations</p>
                </div>
            </div>
        </div>

        <!-- Slide 13: Why Now? -->
        <div class="slide">
            <h2>Why Now?</h2>
            <div class="highlight-box">
                <ul>
                    <li><strong>Provider burnout</strong> is at an all-time high</li>
                    <li>Healthcare <strong>interoperability and AI regulation</strong> have matured</li>
                    <li>Clinics are demanding <strong>smarter tools</strong> and higher efficiency</li>
                </ul>
            </div>
            <div class="metric-box">
                <h3>Perfect Storm</h3>
                <p>Technology readiness meets urgent market need</p>
            </div>
        </div>

        <!-- Slide 14: Closing -->
        <div class="slide cover-slide">
            <h1>Novassist</h1>
            <p>Ready to transform how care is delivered</p>
            <div style="margin-top: 40px; font-size: 1.2em;">
                <p>Backed by clinical, technical, and operational expertise</p>
                <p style="margin-top: 20px;">Contact: <EMAIL> | novassist.ai</p>
            </div>
        </div>
    </div>

    <!-- Navigation -->
    <div class="nav-container">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">Previous</button>
        <div class="slide-counter">
            <span id="currentSlide">1</span> / <span id="totalSlides">14</span>
        </div>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">Next</button>
    </div>

    <script>
        let currentSlideIndex = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;
        
        document.getElementById('totalSlides').textContent = totalSlides;

        function showSlide(index) {
            slides.forEach((slide, i) => {
                slide.classList.remove('active');
                if (i === index) {
                    slide.classList.add('active');
                    slide.classList.add('fade-in');
                }
            });
            
            document.getElementById('currentSlide').textContent = index + 1;
            document.getElementById('prevBtn').disabled = index === 0;
            document.getElementById('nextBtn').disabled = index === totalSlides - 1;
        }

        function changeSlide(direction) {
            const newIndex = currentSlideIndex + direction;
            if (newIndex >= 0 && newIndex < totalSlides) {
                currentSlideIndex = newIndex;
                showSlide(currentSlideIndex);
            }
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => {
            if (e.key === 'ArrowRight' || e.key === ' ') {
                changeSlide(1);
                e.preventDefault();
            } else if (e.key === 'ArrowLeft') {
                changeSlide(-1);
                e.preventDefault();
            }
        });

        // Initialize
        showSlide(0);
    </script>
</body>
</html>